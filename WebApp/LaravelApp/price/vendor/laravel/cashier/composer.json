{"name": "laravel/cashier", "description": "Laravel Cashier provides an expressive, fluent interface to Stripe's subscription billing services.", "keywords": ["laravel", "stripe", "billing"], "license": "MIT", "support": {"issues": "https://github.com/laravel/cashier/issues", "source": "https://github.com/laravel/cashier"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.3|^8.0", "ext-json": "*", "dompdf/dompdf": "^1.2.1|^2.0", "illuminate/console": "^8.37|^9.0|^10.0", "illuminate/contracts": "^8.37|^9.0|^10.0", "illuminate/database": "^8.37|^9.0|^10.0", "illuminate/http": "^8.37|^9.0|^10.0", "illuminate/log": "^8.37|^9.0|^10.0", "illuminate/notifications": "^8.37|^9.0|^10.0", "illuminate/routing": "^8.37|^9.0|^10.0", "illuminate/support": "^8.37|^9.0|^10.0", "illuminate/view": "^8.37|^9.0|^10.0", "moneyphp/money": "^3.2|^4.0", "nesbot/carbon": "^2.0", "stripe/stripe-php": "^7.39|^8.0|^9.0", "symfony/http-kernel": "^5.0|^6.0", "symfony/polyfill-intl-icu": "^1.22.1"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/testbench": "^6.0|^7.0|^8.0", "phpunit/phpunit": "^9.0"}, "suggest": {"ext-intl": "Allows for more locales besides the default \"en\" when formatting money values."}, "autoload": {"psr-4": {"Laravel\\Cashier\\": "src/", "Laravel\\Cashier\\Database\\Factories\\": "database/factories/"}}, "autoload-dev": {"psr-4": {"Laravel\\Cashier\\Tests\\": "tests/"}}, "extra": {"branch-alias": {"dev-master": "13.x-dev"}, "laravel": {"providers": ["Laravel\\Cashier\\CashierServiceProvider"]}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}