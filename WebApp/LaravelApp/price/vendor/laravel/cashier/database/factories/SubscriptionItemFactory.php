<?php

namespace <PERSON><PERSON>\Cashier\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use <PERSON><PERSON>\Cashier\Subscription;
use Laravel\Cashier\SubscriptionItem;

class SubscriptionItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SubscriptionItem::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'subscription_id' => Subscription::factory(),
            'stripe_id' => 'si_'.Str::random(40),
            'stripe_product' => 'prod_'.Str::random(40),
            'stripe_price' => 'price_'.Str::random(40),
            'quantity' => null,
        ];
    }
}
