<?php

namespace Lara<PERSON>\Cashier;

use <PERSON><PERSON>\Cashier\Concerns\HandlesTaxes;
use <PERSON><PERSON>\Cashier\Concerns\ManagesCustomer;
use <PERSON><PERSON>\Cashier\Concerns\ManagesInvoices;
use <PERSON><PERSON>\Cashier\Concerns\ManagesPaymentMethods;
use <PERSON><PERSON>\Cashier\Concerns\ManagesSubscriptions;
use Laravel\Cashier\Concerns\PerformsCharges;

trait Billable
{
    use HandlesTaxes;
    use ManagesCustomer;
    use ManagesInvoices;
    use ManagesPaymentMethods;
    use ManagesSubscriptions;
    use PerformsCharges;
}
