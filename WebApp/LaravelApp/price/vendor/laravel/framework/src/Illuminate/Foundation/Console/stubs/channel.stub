<?php

namespace {{ namespace }};

use {{ namespacedUserModel }};

class {{ class }}
{
    /**
     * Create a new channel instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Authenticate the user's access to the channel.
     *
     * @param  \{{ namespacedUserModel }}  $user
     * @return array|bool
     */
    public function join({{ userModel }} $user)
    {
        //
    }
}
