{"name": "illuminate/bus", "description": "The Illuminate Bus package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.3|^8.0", "illuminate/collections": "^8.0", "illuminate/contracts": "^8.0", "illuminate/pipeline": "^8.0", "illuminate/support": "^8.0"}, "autoload": {"psr-4": {"Illuminate\\Bus\\": ""}}, "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "suggest": {"illuminate/queue": "Required to use closures when chaining jobs (^7.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}