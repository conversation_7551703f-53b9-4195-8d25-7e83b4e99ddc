{"name": "illuminate/broadcasting", "description": "The Illuminate Broadcasting package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.3|^8.0", "ext-json": "*", "psr/log": "^1.0|^2.0", "illuminate/bus": "^8.0", "illuminate/collections": "^8.0", "illuminate/contracts": "^8.0", "illuminate/queue": "^8.0", "illuminate/support": "^8.0"}, "autoload": {"psr-4": {"Illuminate\\Broadcasting\\": ""}}, "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "suggest": {"ably/ably-php": "Required to use the Ably broadcast driver (^1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^4.0|^5.0|^6.0|^7.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}