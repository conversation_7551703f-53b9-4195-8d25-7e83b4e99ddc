{"name": "kreait/firebase-tokens", "description": "A library to work with Firebase tokens", "type": "library", "keywords": ["firebase", "google", "token", "authentication", "auth"], "homepage": "https://github.com/kreait/firebase-token-php", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://github.com/jeromegamez"}], "require": {"php": "^7.4|^8.0", "ext-json": "*", "ext-openssl": "*", "fig/http-message-util": "^1.1.5", "guzzlehttp/guzzle": "^6.3.1|^7.0", "kreait/clock": "^1.1.0", "lcobucci/jwt": "^4.0.4|^4.1.5", "psr/cache": "^1.0|^2.0|^3.0", "psr/simple-cache": "^1.0.1"}, "suggest": {"firebase/php-jwt": "^5.0 can be used to create and parse tokens", "guzzlehttp/guzzle": "^6.2.1|^7.0 can be used as an HTTP handler", "lcobucci/jwt": "^3.2 can be used to create and parse tokens", "psr/cache-implementation": "to cache fetched remote public keys", "psr/simple-cache-implementation": "to cache fetched remote public keys"}, "require-dev": {"firebase/php-jwt": "^5.5.1", "friendsofphp/php-cs-fixer": "^3.10", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^0.12.99", "phpstan/phpstan-phpunit": "^0.12.22", "phpunit/phpunit": "^9.5.23", "rector/rector": "^0.11.60", "symfony/cache": "^5.4.11", "symfony/var-dumper": "^5.4.11"}, "autoload": {"psr-4": {"Kreait\\Firebase\\JWT\\": "src/JWT", "Firebase\\Auth\\Token\\": "src/Firebase/Auth/Token"}}, "autoload-dev": {"psr-4": {"Firebase\\Auth\\Token\\Tests\\": "tests/Firebase/Auth/Token", "Kreait\\Firebase\\JWT\\Tests\\": "tests/JWT"}}, "config": {"sort-packages": true, "allow-plugins": {"phpstan/extension-installer": true}}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jeromegamez"}]}